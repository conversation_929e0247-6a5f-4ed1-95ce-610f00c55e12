<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Simple admin creation tool
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $firstName = trim($_POST['first_name']);
    $lastName = trim($_POST['last_name']);
    
    if (empty($username) || empty($email) || empty($password) || empty($firstName) || empty($lastName)) {
        $message = 'All fields are required.';
        $messageType = 'danger';
    } else {
        $userData = [
            'username' => $username,
            'email' => $email,
            'password' => $password,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'phone' => '',
            'address' => '',
            'role' => 'admin'
        ];
        
        $result = $userManager->register($userData);
        
        if (is_array($result)) {
            if (isset($result['success']) && $result['success']) {
                $message = "Admin account created successfully! Username: $username, Password: $password";
                $messageType = 'success';
            } else {
                $message = $result['message'] ?? 'Failed to create admin account.';
                $messageType = 'danger';
            }
        } else {
            if ($result) {
                $message = "Admin account created successfully! Username: $username, Password: $password";
                $messageType = 'success';
            } else {
                $message = 'Failed to create admin account. Username or email may already exist.';
                $messageType = 'danger';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Admin Account - ZARA-Events</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <div class="container">
        <div class="row justify-content-center" style="min-height: 100vh; align-items: center;">
            <div class="col-md-6">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white text-center">
                        <h3><i class="fas fa-user-shield me-2"></i>Create Admin Account</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                                <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" required
                                       placeholder="Enter unique username (e.g., admin2, manager1)">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required
                                       placeholder="Enter unique email address">
                            </div>
                            
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required
                                       placeholder="Enter first name">
                            </div>
                            
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required
                                       placeholder="Enter last name">
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required
                                       placeholder="Enter password (min 8 characters)">
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-user-plus me-2"></i>Create Admin Account
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <p class="text-muted mb-2">Quick Links:</p>
                            <a href="check_users.php" class="btn btn-outline-info btn-sm me-2">
                                <i class="fas fa-users me-1"></i>View All Users
                            </a>
                            <a href="auth/login.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </div>
                        
                        <div class="mt-3">
                            <h6>Existing Admin Accounts:</h6>
                            <ul class="list-unstyled">
                                <li><strong>Default Admin:</strong> admin / admin123</li>
                                <li><strong>Main Admin:</strong> zara / Hello@94fbr</li>
                                <li><em>Create additional admins with unique usernames/emails</em></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
