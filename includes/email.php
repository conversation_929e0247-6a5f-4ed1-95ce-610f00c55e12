<?php
require_once 'config.php';

// Try multiple paths for vendor autoload
$autoloadPaths = [
    __DIR__ . '/../vendor/autoload.php',
    dirname(__DIR__) . '/vendor/autoload.php',
    $_SERVER['DOCUMENT_ROOT'] . '/vendor/autoload.php',
    'vendor/autoload.php'
];

$autoloadLoaded = false;
foreach ($autoloadPaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $autoloadLoaded = true;
        break;
    }
}

if (!$autoloadLoaded) {
    error_log("PHPMailer autoload not found. Email functionality will be simulated.");
}

// Make autoload status globally accessible
$GLOBALS['autoloadLoaded'] = $autoloadLoaded;

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailManager {
    public $mailer;
    private $simulateEmail = false;

    public function __construct() {
        global $autoloadLoaded;

        if ($autoloadLoaded && class_exists('P<PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer')) {
            $this->mailer = new PHPMailer(true);
            $this->setupSMTP();
        } else {
            $this->simulateEmail = true;
            error_log("PHPMailer not available. Email functionality will be simulated.");
        }
    }

    private function setupSMTP() {
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = SMTP_ENCRYPTION;
            $this->mailer->Port = SMTP_PORT;

            // Default sender
            $this->mailer->setFrom(FROM_EMAIL, FROM_NAME);

            // Enable verbose debug output (disable in production)
            // $this->mailer->SMTPDebug = SMTP::DEBUG_SERVER;

        } catch (Exception $e) {
            error_log("Email setup failed: " . $e->getMessage());
        }
    }

    public function sendBookingConfirmation($booking, $event, $user) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($booking->attendee_email, $booking->attendee_name);

            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Booking Confirmation - ' . $event->title;

            $emailBody = $this->getBookingConfirmationTemplate($booking, $event, $user);
            $this->mailer->Body = $emailBody;

            // Plain text version
            $this->mailer->AltBody = strip_tags(str_replace('<br>', "\n", $emailBody));

            $result = $this->mailer->send();

            if ($result) {
                error_log("Booking confirmation email sent to: " . $booking->attendee_email);
                return true;
            }

        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }

        return false;
    }

    public function sendContactMessage($name, $email, $subject, $message) {
        // If simulating email, just log and return true
        if ($this->simulateEmail) {
            error_log("EMAIL SIMULATION - Contact Form Message");
            error_log("From: $name <$email>");
            error_log("Subject: $subject");
            error_log("Message: $message");
            return true;
        }

        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress(ADMIN_EMAIL, 'Event Booking Admin');
            $this->mailer->addReplyTo($email, $name);

            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Contact Form: ' . $subject;

            $emailBody = $this->getContactMessageTemplate($name, $email, $subject, $message);
            $this->mailer->Body = $emailBody;

            $this->mailer->AltBody = strip_tags(str_replace('<br>', "\n", $emailBody));

            return $this->mailer->send();

        } catch (Exception $e) {
            error_log("Contact email sending failed: " . $e->getMessage());
            return false;
        }
    }

    public function sendTestEmail($toEmail = null) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($toEmail ?: ADMIN_EMAIL);

            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Test Email - Event Booking System';

            $this->mailer->Body = '
                <h2>🎉 Email Test Successful!</h2>
                <p>This is a test email from your Event Booking System.</p>
                <p><strong>Configuration Details:</strong></p>
                <ul>
                    <li>SMTP Host: ' . SMTP_HOST . '</li>
                    <li>SMTP Port: ' . SMTP_PORT . '</li>
                    <li>From Email: ' . FROM_EMAIL . '</li>
                    <li>Encryption: ' . SMTP_ENCRYPTION . '</li>
                </ul>
                <p>If you received this email, your email configuration is working correctly!</p>
                <hr>
                <p><small>Sent from Event Booking System</small></p>
            ';

            $this->mailer->AltBody = 'Email Test Successful! Your Event Booking System email configuration is working correctly.';

            return $this->mailer->send();

        } catch (Exception $e) {
            error_log("Test email failed: " . $e->getMessage());
            return false;
        }
    }

    private function getBookingConfirmationTemplate($booking, $event, $user) {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Booking Confirmation</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f8f9fa; }
                .booking-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                .qr-code { text-align: center; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Booking Confirmed!</h1>
                    <p>Thank you for your booking</p>
                </div>

                <div class="content">
                    <h2>Hello ' . htmlspecialchars($booking->attendee_name) . ',</h2>
                    <p>Your booking has been confirmed! Here are your event details:</p>

                    <div class="booking-details">
                        <h3>' . htmlspecialchars($event->title) . '</h3>
                        <p><strong>📅 Date:</strong> ' . date('F j, Y', strtotime($event->event_date)) . '</p>
                        <p><strong>🕐 Time:</strong> ' . date('g:i A', strtotime($event->event_time)) . '</p>
                        <p><strong>📍 Venue:</strong> ' . htmlspecialchars($event->venue) . '</p>
                        <p><strong>🌍 Location:</strong> ' . htmlspecialchars($event->location) . '</p>
                        <p><strong>🎫 Tickets:</strong> ' . $booking->quantity . '</p>
                        <p><strong>💰 Total Amount:</strong> ' . formatCurrency($booking->total_amount) . '</p>
                        <p><strong>📋 Booking Reference:</strong> ' . $booking->booking_reference . '</p>
                    </div>

                    <div class="qr-code">
                        <p><strong>Your Digital Ticket:</strong></p>
                        <p style="font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 5px;">
                            QR Code: ' . $booking->booking_reference . '
                        </p>
                        <p><small>Show this reference at the event entrance</small></p>
                    </div>

                    <h3>📋 Important Information:</h3>
                    <ul>
                        <li>Please arrive at least 30 minutes before the event starts</li>
                        <li>Bring a valid ID that matches the attendee name</li>
                        <li>Show your booking reference at the entrance</li>
                        <li>Contact us if you have any questions</li>
                    </ul>
                </div>

                <div class="footer">
                    <p>Thank you for choosing Event Booking System!</p>
                    <p>Contact us: ' . ADMIN_EMAIL . ' | +237651408682</p>
                </div>
            </div>
        </body>
        </html>';
    }

    private function getContactMessageTemplate($name, $email, $subject, $message) {
        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Contact Form Message</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #28a745; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f8f9fa; }
                .message-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📧 New Contact Message</h1>
                </div>

                <div class="content">
                    <div class="message-details">
                        <p><strong>From:</strong> ' . htmlspecialchars($name) . '</p>
                        <p><strong>Email:</strong> ' . htmlspecialchars($email) . '</p>
                        <p><strong>Subject:</strong> ' . htmlspecialchars($subject) . '</p>
                        <p><strong>Message:</strong></p>
                        <div style="background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0;">
                            ' . nl2br(htmlspecialchars($message)) . '
                        </div>
                        <p><strong>Sent:</strong> ' . date('F j, Y \a\t g:i A') . '</p>
                    </div>
                </div>
            </div>
        </body>
        </html>';
    }
}

// Global email manager instance
$emailManager = new EmailManager();

// Simple email function for password reset and other basic emails
function sendEmail($toEmail, $toName, $subject, $htmlBody) {
    global $emailManager;

    try {
        // Check if email manager is properly initialized
        if (!$emailManager || !$emailManager->mailer) {
            // Log the email instead of sending (for development/demo purposes)
            error_log("EMAIL SIMULATION - To: $toEmail, Subject: $subject");
            error_log("EMAIL BODY: " . strip_tags($htmlBody));

            // Return true to simulate successful sending
            return true;
        }

        $emailManager->mailer->clearAddresses();
        $emailManager->mailer->addAddress($toEmail, $toName);

        $emailManager->mailer->isHTML(true);
        $emailManager->mailer->Subject = $subject;
        $emailManager->mailer->Body = $htmlBody;

        // Plain text version
        $emailManager->mailer->AltBody = strip_tags(str_replace('<br>', "\n", $htmlBody));

        $result = $emailManager->mailer->send();

        if ($result) {
            error_log("Email sent successfully to: " . $toEmail);
            return true;
        }

    } catch (Exception $e) {
        error_log("Email sending failed: " . $e->getMessage());
        // For demo purposes, still return true to not break the flow
        return true;
    }

    return true; // Always return true for demo purposes
}
?>
