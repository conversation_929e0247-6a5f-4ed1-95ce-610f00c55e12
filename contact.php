<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/email.php';

$pageTitle = 'Contact Us';
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $messageText = sanitizeInput($_POST['message'] ?? '');
    $category = sanitizeInput($_POST['category'] ?? '');

    // Validation
    if (empty($name) || empty($email) || empty($subject) || empty($messageText)) {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    } else {
        // Send email
        global $emailManager;
        $fullSubject = "[$category] $subject";

        if ($emailManager->sendContactMessage($name, $email, $fullSubject, $messageText)) {
            $message = 'Thank you for your message! We will get back to you soon.';
            $messageType = 'success';
            // Clear form data on success
            $name = $email = $subject = $messageText = $category = '';
        } else {
            $message = 'Sorry, there was an error sending your message. Please try again or contact us directly.';
            $messageType = 'error';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { padding-top: 80px; }
        .navbar { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); }
        .contact-hero { background: linear-gradient(135deg, #408681, #5ba09b); color: white; padding: 60px 0; }
        .contact-card { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-top: -30px; position: relative; z-index: 2; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-calendar-star me-2"></i>ZARA-Events
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="about.php">About</a>
                <a class="nav-link active" href="contact.php">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Contact Hero -->
    <section class="contact-hero">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-3">Get In Touch</h1>
            <p class="lead">Have questions about our events or need assistance? We're here to help!</p>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="py-5">
        <div class="container">
            <div class="contact-card">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <h2 class="fw-bold mb-4">Send us a Message</h2>

                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="General Inquiry" <?php echo ($category ?? '') === 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                                        <option value="Event Booking" <?php echo ($category ?? '') === 'Event Booking' ? 'selected' : ''; ?>>Event Booking</option>
                                        <option value="Technical Support" <?php echo ($category ?? '') === 'Technical Support' ? 'selected' : ''; ?>>Technical Support</option>
                                        <option value="Partnership" <?php echo ($category ?? '') === 'Partnership' ? 'selected' : ''; ?>>Partnership</option>
                                        <option value="Feedback" <?php echo ($category ?? '') === 'Feedback' ? 'selected' : ''; ?>>Feedback</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" value="<?php echo htmlspecialchars($subject ?? ''); ?>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="6" required><?php echo htmlspecialchars($messageText ?? ''); ?></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </form>
                    </div>

                    <div class="col-lg-4">
                        <div class="bg-light p-4 rounded">
                            <h4 class="fw-bold mb-4">Contact Information</h4>
                            <div class="mb-3">
                                <h6 class="fw-bold"><i class="fas fa-envelope me-2 text-primary"></i>Email</h6>
                                <p class="text-muted"><EMAIL></p>
                            </div>
                            <div class="mb-3">
                                <h6 class="fw-bold"><i class="fas fa-phone me-2 text-primary"></i>Phone</h6>
                                <p class="text-muted">+237 651 408 682</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="fw-bold"><i class="fas fa-map-marker-alt me-2 text-primary"></i>Location</h6>
                                <p class="text-muted">Yaoundé, Cameroon</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="fw-bold"><i class="fas fa-clock me-2 text-primary"></i>Response Time</h6>
                                <p class="text-muted">Within 24 hours</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Contact -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-12 mb-4">
                    <h3 class="fw-bold">Quick Contact Options</h3>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100 text-center">
                        <div class="card-body">
                            <i class="fab fa-whatsapp fa-3x text-success mb-3"></i>
                            <h5 class="fw-bold">WhatsApp</h5>
                            <a href="https://wa.me/237651408682" class="btn btn-success" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i>Chat Now
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                            <h5 class="fw-bold">Email</h5>
                            <a href="mailto:<EMAIL>" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>Send Email
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-phone fa-3x text-warning mb-3"></i>
                            <h5 class="fw-bold">Phone</h5>
                            <a href="tel:+237651408682" class="btn btn-warning">
                                <i class="fas fa-phone me-2"></i>Call Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p>&copy; 2024 ZARA-Events. All rights reserved. | Developed by Tayong Fritz</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
