<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

echo "<h2>Testing Admin Login for User 'zara'</h2>";

// Test 1: Check if user exists
echo "<h3>Test 1: User Verification</h3>";
$db->query('SELECT * FROM users WHERE username = :username');
$db->bind(':username', 'zara');
$user = $db->single();

if ($user) {
    echo "<p>✅ User 'zara' exists</p>";
    echo "<p>Email: " . htmlspecialchars($user->email) . "</p>";
    echo "<p>Role: " . htmlspecialchars($user->role) . "</p>";
    echo "<p>Name: " . htmlspecialchars($user->first_name . ' ' . $user->last_name) . "</p>";
} else {
    echo "<p>❌ User 'zara' not found</p>";
    exit;
}

// Test 2: Password verification
echo "<h3>Test 2: Password Verification</h3>";
$password = 'Hello@94fbr';
if (password_verify($password, $user->password)) {
    echo "<p>✅ Password verification successful</p>";
} else {
    echo "<p>❌ Password verification failed</p>";
    echo "<p>Stored hash: " . htmlspecialchars($user->password) . "</p>";
}

// Test 3: Login simulation
echo "<h3>Test 3: Login Simulation</h3>";
$loginResult = $userManager->login('zara', $password);

if ($loginResult) {
    echo "<p>✅ Login successful</p>";
    echo "<p>Session user_id: " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
    echo "<p>Session user_role: " . ($_SESSION['user_role'] ?? 'Not set') . "</p>";
    echo "<p>Session first_name: " . ($_SESSION['first_name'] ?? 'Not set') . "</p>";
    
    // Test admin check
    if (isAdmin()) {
        echo "<p>✅ User is confirmed as admin</p>";
        echo "<p>🎯 <strong>Admin login successful! User should be redirected to /admin/</strong></p>";
    } else {
        echo "<p>❌ User is not recognized as admin</p>";
    }
} else {
    echo "<p>❌ Login failed</p>";
}

// Test 4: Direct admin access check
echo "<h3>Test 4: Admin Access Check</h3>";
if (isLoggedIn() && isAdmin()) {
    echo "<p>✅ Admin access confirmed</p>";
    echo "<p><a href='admin/' target='_blank'>🔗 Click here to access Admin Panel</a></p>";
} else {
    echo "<p>❌ Admin access denied</p>";
}

echo "<hr>";
echo "<h3>Quick Links</h3>";
echo "<p><a href='auth/login.php'>🔗 User Login Page</a></p>";
echo "<p><a href='admin/login.php'>🔗 Admin Login Page</a></p>";
echo "<p><a href='admin/'>🔗 Admin Dashboard</a></p>";
echo "<p><a href='auth/logout.php'>🔗 Logout</a></p>";

echo "<hr>";
echo "<h3>Login Instructions</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>";
echo "<p><strong>Admin Login Credentials:</strong></p>";
echo "<p>Username: <code>zara</code></p>";
echo "<p>Password: <code>Hello@94fbr</code></p>";
echo "<p><strong>Login Process:</strong></p>";
echo "<ol>";
echo "<li>Go to <a href='auth/login.php'>User Login Page</a> or <a href='admin/login.php'>Admin Login Page</a></li>";
echo "<li>Enter the credentials above</li>";
echo "<li>Upon successful login, admin users will be automatically redirected to the Admin Dashboard</li>";
echo "</ol>";
echo "</div>";
?>
